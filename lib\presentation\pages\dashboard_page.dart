import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';

class DashboardPage extends ConsumerWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    return Padding(
      padding: const EdgeInsets.all(16),
      child: user == null
          ? const Center(
              child: Text('Tidak ada data user.'),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Selamat datang, ${user.name}!',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 16),
                Text('ID: ${user.id}'),
                Text('Email: ${user.email}'),
                Text('Role: ${user.role}'),
                Text('Komplek ID: ${user.komplekId ?? "-"}'),
              ],
            ),
    );
  }
}
