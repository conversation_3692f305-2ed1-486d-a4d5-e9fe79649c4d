import 'package:flutter/material.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

class AbsensiPage extends StatefulWidget {
  const AbsensiPage({super.key});

  @override
  State<AbsensiPage> createState() => _AbsensiPageState();
}

class _AbsensiPageState extends State<AbsensiPage> with ErrorHandlerMixin {
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_hasError) {
      return ErrorStateWidget(
        error: _errorMessage ?? 'Terja<PERSON> kesalahan',
        onRetry: _loadData,
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          const Text(
            'Absensi Tahfidz',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          // Demo error handling buttons
          Wrap(
            spacing: 8,
            children: [
              ElevatedButton(
                onPressed: () => _simulateError('network'),
                child: const Text('Test Network Error'),
              ),
              ElevatedButton(
                onPressed: () => _simulateError('server'),
                child: const Text('Test Server Error'),
              ),
              ElevatedButton(
                onPressed: () => _simulateError('validation'),
                child: const Text('Test Validation Error'),
              ),
              ElevatedButton(
                onPressed: () => _simulateError('unauthorized'),
                child: const Text('Test Unauthorized'),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Demo compact error
          if (_errorMessage != null)
            CompactErrorWidget(
              error: _errorMessage!,
              onRetry: () {
                setState(() {
                  _errorMessage = null;
                });
              },
            ),
        ],
      ),
    );
  }

  void _loadData() {
    setState(() {
      _isLoading = true;
      _hasError = false;
      _errorMessage = null;
    });

    // Simulate loading
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _simulateError(String type) {
    String errorMessage;

    switch (type) {
      case 'network':
        errorMessage = 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
        break;
      case 'server':
        errorMessage = 'Terjadi kesalahan pada server. Coba lagi nanti.';
        break;
      case 'validation':
        errorMessage = 'Data yang dikirim tidak valid. Periksa kembali input Anda.';
        break;
      case 'unauthorized':
        errorMessage = 'Sesi Anda telah berakhir. Silakan login kembali.';
        break;
      default:
        errorMessage = 'Terjadi kesalahan yang tidak diketahui.';
    }

    // Show error snackbar
    showErrorSnackBar(errorMessage);

    // Set error state
    setState(() {
      _errorMessage = errorMessage;
    });
  }
}
