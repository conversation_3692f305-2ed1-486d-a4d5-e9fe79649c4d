import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/core/network/api_client.dart';
import 'package:sistem_pondok_apps/core/storage/secure_storage.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';
import 'package:sistem_pondok_apps/domain/entities/user_entity.dart';

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

class AuthState {
  final bool isLoading;
  final bool isInitialized; // Tambahkan flag untuk menandai apakah sudah selesai check initial auth
  final String? error;
  final UserEntity? user;

  AuthState({
    this.isLoading = false,
    this.isInitialized = false,
    this.error,
    this.user
  });

  AuthState copyWith({
    bool? isLoading,
    bool? isInitialized,
    String? error,
    UserEntity? user
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isInitialized: isInitialized ?? this.isInitialized,
      error: error,
      user: user ?? this.user,
    );
  }
}

class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(AuthState()) {
    _loadUser();
  }

  final _api = ApiClient();
  final _storage = SecureStorage();

  /// Auto load user saat app start
  Future<void> _loadUser() async {
    final token = await _storage.getToken();

    if (token == null) {
      // Tidak ada token, set sebagai initialized tapi tidak login
      state = state.copyWith(isInitialized: true);
      return;
    }

    _api.setToken(token);

    try {
      final response = await _api.dio.get('/me');
      final user = UserEntity.fromJson(response.data);

      state = state.copyWith(user: user, isInitialized: true);
      debugPrint('✅ User loaded from /me: ${user.name}');
    } catch (e) {
      debugPrint('❌ Gagal load user: $e');
      await _storage.deleteToken();
      _api.clearToken();
      state = state.copyWith(isInitialized: true); // Set initialized meski gagal
    }
  }

  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final response = await _api.dio.post('/login', data: {
        'email': email,
        'password': password,
      });

      final data = response.data;

      if (data['success'] == true) {
        final token = data['token'];
        await _storage.saveToken(token);
        _api.setToken(token);

        final user = UserEntity.fromJson(data['user']);
        state = state.copyWith(user: user, isInitialized: true);
      } else {
        state = state.copyWith(error: data['message'] ?? 'Login gagal.');
      }
    } catch (e) {
      state = state.copyWith(error: ErrorHandler.parseError(e));
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> logout() async {
    await _storage.deleteToken();
    _api.clearToken();
    state = AuthState(isInitialized: true); // Reset tapi tetap initialized
  }
}
