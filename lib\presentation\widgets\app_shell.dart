import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';

class AppShell extends ConsumerWidget {
  final Widget child;

  const AppShell({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isWide = screenWidth >= 768; // Tablet landscape dan desktop
    final isMedium = screenWidth >= 600 && screenWidth < 768; // Tablet portrait

    if (isWide || isMedium) {
      // Desktop/Tablet layout dengan NavigationRail
      return Scaffold(
        appBar: AppBar(
          title: Text(_getPageTitle(context)),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {
                ref.read(authProvider.notifier).logout();
              },
              tooltip: 'Logout',
            ),
          ],
        ),
        body: Row(
          children: [
            NavigationRail(
              selectedIndex: _getSelectedIndex(context),
              onDestinationSelected: (index) => _onDestinationSelected(index, context),
              extended: isWide, // Extended hanya untuk desktop
              destinations: const [
                NavigationRailDestination(
                  icon: Icon(Icons.dashboard),
                  label: Text('Dashboard'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.assignment_turned_in),
                  label: Text('Absensi Tahfidz'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.class_),
                  label: Text('Kelas'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.person),
                  label: Text('Ustadz'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.settings),
                  label: Text('Pengaturan'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.account_circle),
                  label: Text('Akun'),
                ),
              ],
            ),
            Expanded(child: child),
          ],
        ),
      );
    } else {
      // Mobile layout dengan Drawer
      return Scaffold(
        appBar: AppBar(
          title: Text(_getPageTitle(context)),
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          actions: [
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: () {
                ref.read(authProvider.notifier).logout();
              },
              tooltip: 'Logout',
            ),
          ],
        ),
        drawer: NavigationDrawer(
          selectedIndex: _getSelectedIndex(context),
          onDestinationSelected: (index) {
            _onDestinationSelected(index, context);
            Navigator.of(context).pop(); // Tutup drawer setelah navigasi
          },
          children: [
            // Header drawer
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Icon(
                    Icons.mosque,
                    size: 48,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Sistem Pondok',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            // Menu items
            const NavigationDrawerDestination(
              icon: Icon(Icons.dashboard),
              label: Text('Dashboard'),
            ),
            const NavigationDrawerDestination(
              icon: Icon(Icons.assignment_turned_in),
              label: Text('Absensi Tahfidz'),
            ),
            const NavigationDrawerDestination(
              icon: Icon(Icons.class_),
              label: Text('Kelas'),
            ),
            const NavigationDrawerDestination(
              icon: Icon(Icons.person),
              label: Text('Ustadz'),
            ),
            const NavigationDrawerDestination(
              icon: Icon(Icons.settings),
              label: Text('Pengaturan'),
            ),
            const NavigationDrawerDestination(
              icon: Icon(Icons.account_circle),
              label: Text('Akun'),
            ),
          ],
        ),
        body: child,
      );
    }
  }

  int _getSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/setoran/absensi')) return 1;
    if (location.startsWith('/setoran/kelas')) return 2;
    if (location.startsWith('/ustadz')) return 3;
    if (location.startsWith('/pengaturan/absensi')) return 4;
    if (location.startsWith('/akun')) return 5;
    return 0;
  }

  String _getPageTitle(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/dashboard')) return 'Dashboard';
    if (location.startsWith('/setoran/absensi')) return 'Absensi Tahfidz';
    if (location.startsWith('/setoran/kelas')) return 'Kelas';
    if (location.startsWith('/ustadz')) return 'Ustadz';
    if (location.startsWith('/pengaturan/absensi')) return 'Pengaturan Absensi';
    if (location.startsWith('/akun')) return 'Akun';
    return 'Sistem Pondok';
  }

  void _onDestinationSelected(int index, BuildContext context) {
    switch (index) {
      case 0:
        context.go('/dashboard');
        break;
      case 1:
        context.go('/setoran/absensi');
        break;
      case 2:
        context.go('/setoran/kelas');
        break;
      case 3:
        context.go('/ustadz');
        break;
      case 4:
        context.go('/pengaturan/absensi');
        break;
      case 5:
        context.go('/akun');
        break;
    }
  }
}
