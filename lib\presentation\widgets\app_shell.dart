import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class AppShell extends StatelessWidget {
  final Widget child;

  const AppShell({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width >= 600;

    return Scaffold(
      body: Row(
        children: [
          if (isWide)
            NavigationRail(
              selectedIndex: _getSelectedIndex(context),
              onDestinationSelected: (index) => _onDestinationSelected(index, context),
              labelType: NavigationRailLabelType.all,
              destinations: const [
                NavigationRailDestination(
                  icon: Icon(Icons.dashboard),
                  label: Text('Dashboard'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.assignment_turned_in),
                  label: Text('Absensi Tahfidz'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.class_),
                  label: Text('Kelas'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.person),
                  label: Text('Ustadz'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.settings),
                  label: Text('Pengaturan'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.account_circle),
                  label: Text('Akun'),
                ),
              ],
            )
          else
            NavigationDrawer(
              selectedIndex: _getSelectedIndex(context),
              onDestinationSelected: (index) => _onDestinationSelected(index, context),
              children: const [
                NavigationDrawerDestination(
                  icon: Icon(Icons.dashboard),
                  label: Text('Dashboard'),
                ),
                NavigationDrawerDestination(
                  icon: Icon(Icons.assignment_turned_in),
                  label: Text('Absensi Tahfidz'),
                ),
                NavigationDrawerDestination(
                  icon: Icon(Icons.class_),
                  label: Text('Kelas'),
                ),
                NavigationDrawerDestination(
                  icon: Icon(Icons.person),
                  label: Text('Ustadz'),
                ),
                NavigationDrawerDestination(
                  icon: Icon(Icons.settings),
                  label: Text('Pengaturan'),
                ),
                NavigationDrawerDestination(
                  icon: Icon(Icons.account_circle),
                  label: Text('Akun'),
                ),
              ],
            ),
          Expanded(child: child),
        ],
      ),
    );
  }

  int _getSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;
    if (location.startsWith('/dashboard')) return 0;
    if (location.startsWith('/setoran/absensi')) return 1;
    if (location.startsWith('/setoran/kelas')) return 2;
    if (location.startsWith('/setoran/ustadz')) return 3;
    if (location.startsWith('/pengaturan/absensi')) return 4;
    if (location.startsWith('/akun')) return 5;
    return 0;
  }

  void _onDestinationSelected(int index, BuildContext context) {
    switch (index) {
      case 0:
        context.go('/dashboard');
        break;
      case 1:
        context.go('/setoran/absensi');
        break;
      case 2:
        context.go('/setoran/kelas');
        break;
      case 3:
        context.go('/setoran/ustadz');
        break;
      case 4:
        context.go('/pengaturan/absensi');
        break;
      case 5:
        context.go('/akun');
        break;
    }
  }
}
