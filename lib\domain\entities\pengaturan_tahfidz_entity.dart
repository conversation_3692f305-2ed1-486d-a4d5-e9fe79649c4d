class PengaturanTahfidzEntity {
  final int id;
  final String mode;
  final DateTime berlakuMulai;
  final bool aktif;

  PengaturanTahfidzEntity({
    required this.id,
    required this.mode,
    required this.berlakuMulai,
    required this.aktif,
  });

  factory PengaturanTahfidzEntity.fromJson(Map<String, dynamic> json) {
    return PengaturanTahfidzEntity(
      id: json['id'],
      mode: json['mode'],
      berlakuMulai: DateTime.parse(json['berlaku_mulai']),
      aktif: json['aktif'] ?? true,
    );
  }
}
