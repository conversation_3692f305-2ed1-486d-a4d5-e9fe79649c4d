/// Contoh penggunaan ErrorHandler untuk berbagai skenario
/// 
/// File ini berisi contoh-contoh implementasi error handling
/// yang user-friendly untuk berbagai kasus penggunaan.

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

/// Contoh 1: Error handling di Provider
class ExampleProvider extends StateNotifier<AsyncValue<String>> {
  ExampleProvider() : super(const AsyncLoading());

  Future<void> fetchData() async {
    try {
      state = const AsyncLoading();
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Simulate error
      throw Exception('Network error');
      
    } catch (error, stackTrace) {
      // Gunakan ErrorHandler untuk parsing error
      final friendlyMessage = ErrorHandler.parseError(error);
      state = AsyncError(friendlyMessage, stackTrace);
    }
  }
}

/// Contoh 2: Error handling di StatefulWidget dengan Mixin
class ExamplePageWithMixin extends StatefulWidget {
  const ExamplePageWithMixin({super.key});

  @override
  State<ExamplePageWithMixin> createState() => _ExamplePageWithMixinState();
}

class _ExamplePageWithMixinState extends State<ExamplePageWithMixin> 
    with ErrorHandlerMixin {
  
  bool _isLoading = false;
  String? _data;
  Object? _error;

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return buildErrorState(_error!, onRetry: _loadData);
    }

    return Column(
      children: [
        Text(_data ?? 'No data'),
        ElevatedButton(
          onPressed: _loadData,
          child: const Text('Load Data'),
        ),
        ElevatedButton(
          onPressed: _simulateError,
          child: const Text('Simulate Error'),
        ),
      ],
    );
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        _data = 'Data loaded successfully';
        _isLoading = false;
      });
    } catch (error) {
      setState(() {
        _error = error;
        _isLoading = false;
      });
    }
  }

  void _simulateError() {
    final error = Exception('This is a simulated error');
    
    // Show error dialog
    showErrorDialog(error, title: 'Simulation Error');
    
    // Show error snackbar
    showErrorSnackBar(error);
    
    // Set error state
    setState(() {
      _error = error;
    });
  }
}

/// Contoh 3: Error handling di Consumer Widget
class ExampleConsumerWidget extends ConsumerWidget {
  const ExampleConsumerWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final asyncValue = ref.watch(exampleProvider);

    return asyncValue.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      
      // Gunakan ErrorStateWidget untuk error
      error: (error, stackTrace) => ErrorStateWidget(
        error: error,
        onRetry: () => ref.read(exampleProvider.notifier).fetchData(),
      ),
      
      data: (data) => Column(
        children: [
          Text(data),
          ElevatedButton(
            onPressed: () => ref.read(exampleProvider.notifier).fetchData(),
            child: const Text('Refresh'),
          ),
        ],
      ),
    );
  }
}

/// Contoh 4: Error handling untuk form submission
class ExampleFormWidget extends StatefulWidget {
  const ExampleFormWidget({super.key});

  @override
  State<ExampleFormWidget> createState() => _ExampleFormWidgetState();
}

class _ExampleFormWidgetState extends State<ExampleFormWidget> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  bool _isSubmitting = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          TextFormField(
            controller: _nameController,
            decoration: const InputDecoration(labelText: 'Name'),
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Name is required';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          // Show error banner if there's an error
          if (_errorMessage != null)
            ErrorBannerWidget(
              error: _errorMessage!,
              onDismiss: () => setState(() => _errorMessage = null),
              onRetry: _submitForm,
            ),
          
          const SizedBox(height: 16),
          
          ElevatedButton(
            onPressed: _isSubmitting ? null : _submitForm,
            child: _isSubmitting 
                ? const CircularProgressIndicator()
                : const Text('Submit'),
          ),
        ],
      ),
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
      _errorMessage = null;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 2));
      
      // Simulate random error
      if (DateTime.now().millisecond % 2 == 0) {
        throw Exception('Validation failed: Name already exists');
      }

      // Success
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Form submitted successfully!')),
        );
      }
      
    } catch (error) {
      setState(() {
        _errorMessage = ErrorHandler.parseError(error);
      });
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }
}

/// Provider untuk contoh
final exampleProvider = StateNotifierProvider<ExampleProvider, AsyncValue<String>>(
  (ref) => ExampleProvider(),
);

/// Contoh 5: Error handling untuk list data
class ExampleListWidget extends StatefulWidget {
  const ExampleListWidget({super.key});

  @override
  State<ExampleListWidget> createState() => _ExampleListWidgetState();
}

class _ExampleListWidgetState extends State<ExampleListWidget> {
  List<String> _items = [];
  bool _isLoading = false;
  Object? _error;

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _items.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null && _items.isEmpty) {
      return ErrorStateWidget(
        error: _error!,
        onRetry: _loadItems,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadItems,
      child: Column(
        children: [
          // Show error banner if there's an error but we have cached data
          if (_error != null && _items.isNotEmpty)
            ErrorBannerWidget(
              error: _error!,
              onRetry: _loadItems,
              onDismiss: () => setState(() => _error = null),
            ),
          
          Expanded(
            child: ListView.builder(
              itemCount: _items.length,
              itemBuilder: (context, index) {
                return ListTile(
                  title: Text(_items[index]),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _loadItems() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Simulate random error
      if (DateTime.now().millisecond % 3 == 0) {
        throw Exception('Failed to load items');
      }

      setState(() {
        _items = List.generate(10, (index) => 'Item ${index + 1}');
        _isLoading = false;
      });
      
    } catch (error) {
      setState(() {
        _error = error;
        _isLoading = false;
      });
    }
  }
}
