import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

/// Utility class untuk handling error dengan pesan yang user-friendly
class ErrorHandler {
  /// Parse error dari berbagai sumber menjadi pesan yang user-friendly
  static String parseError(dynamic error) {
    if (error is DioException) {
      return _parseDioError(error);
    }
    
    if (error is String) {
      return error;
    }
    
    return 'Terjadi kesalahan yang tidak diketahui';
  }

  /// Parse DioException menjadi pesan yang user-friendly
  static String _parseDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'Koneksi timeout. Periksa koneksi internet Anda.';
      
      case DioExceptionType.sendTimeout:
        return 'Gagal mengirim data. Periksa koneksi internet Anda.';
      
      case DioExceptionType.receiveTimeout:
        return 'Server tidak merespons. Coba lagi nanti.';
      
      case DioExceptionType.badCertificate:
        return 'Sertifikat keamanan tidak valid.';
      
      case DioExceptionType.badResponse:
        return _parseHttpError(error);
      
      case DioExceptionType.cancel:
        return 'Permintaan dibatalkan.';
      
      case DioExceptionType.connectionError:
        return 'Tidak dapat terhubung ke server. Periksa koneksi internet Anda.';
      
      case DioExceptionType.unknown:
        return 'Terjadi kesalahan yang tidak diketahui. Coba lagi nanti.';
    }
  }

  /// Parse HTTP error response
  static String _parseHttpError(DioException error) {
    final statusCode = error.response?.statusCode;
    final responseData = error.response?.data;

    switch (statusCode) {
      case 400:
        return _extractErrorMessage(responseData) ?? 'Permintaan tidak valid.';
      
      case 401:
        return 'Sesi Anda telah berakhir. Silakan login kembali.';
      
      case 403:
        return 'Anda tidak memiliki izin untuk melakukan tindakan ini.';
      
      case 404:
        return 'Data yang diminta tidak ditemukan.';
      
      case 422:
        return _extractValidationError(responseData) ?? 'Data yang dikirim tidak valid.';
      
      case 429:
        return 'Terlalu banyak permintaan. Coba lagi nanti.';
      
      case 500:
        return 'Terjadi kesalahan pada server. Coba lagi nanti.';
      
      case 502:
        return 'Server sedang bermasalah. Coba lagi nanti.';
      
      case 503:
        return 'Server sedang dalam pemeliharaan. Coba lagi nanti.';
      
      default:
        return _extractErrorMessage(responseData) ?? 
               'Terjadi kesalahan pada server (${statusCode ?? 'Unknown'}).';
    }
  }

  /// Extract error message dari response data
  static String? _extractErrorMessage(dynamic responseData) {
    if (responseData == null) return null;
    
    if (responseData is Map<String, dynamic>) {
      // Coba ambil dari field 'message'
      if (responseData['message'] is String) {
        return responseData['message'];
      }
      
      // Coba ambil dari field 'error'
      if (responseData['error'] is String) {
        return responseData['error'];
      }
      
      // Coba ambil dari field 'detail'
      if (responseData['detail'] is String) {
        return responseData['detail'];
      }
    }
    
    return null;
  }

  /// Extract validation error dari response data (untuk error 422)
  static String? _extractValidationError(dynamic responseData) {
    if (responseData == null) return null;
    
    if (responseData is Map<String, dynamic>) {
      // Laravel validation error format
      if (responseData['errors'] is Map) {
        final errors = responseData['errors'] as Map<String, dynamic>;
        final firstError = errors.values.first;
        if (firstError is List && firstError.isNotEmpty) {
          return firstError.first.toString();
        }
      }
      
      // Coba ambil message biasa
      return _extractErrorMessage(responseData);
    }
    
    return null;
  }

  /// Show error dialog dengan pesan yang user-friendly
  static void showErrorDialog(BuildContext context, dynamic error, {String? title}) {
    final errorMessage = parseError(error);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title ?? 'Terjadi Kesalahan'),
        content: Text(errorMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Show error snackbar dengan pesan yang user-friendly
  static void showErrorSnackBar(BuildContext context, dynamic error) {
    final errorMessage = parseError(error);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        action: SnackBarAction(
          label: 'OK',
          textColor: Colors.white,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  /// Check apakah error adalah unauthorized (401)
  static bool isUnauthorized(dynamic error) {
    if (error is DioException) {
      return error.response?.statusCode == 401;
    }
    return false;
  }

  /// Check apakah error adalah network error
  static bool isNetworkError(dynamic error) {
    if (error is DioException) {
      return error.type == DioExceptionType.connectionError ||
             error.type == DioExceptionType.connectionTimeout ||
             error.type == DioExceptionType.receiveTimeout ||
             error.type == DioExceptionType.sendTimeout;
    }
    return false;
  }

  /// Check apakah error adalah server error (5xx)
  static bool isServerError(dynamic error) {
    if (error is DioException) {
      final statusCode = error.response?.statusCode;
      return statusCode != null && statusCode >= 500 && statusCode < 600;
    }
    return false;
  }
}

/// Extension untuk memudahkan penggunaan error handler
extension ErrorHandlerExtension on dynamic {
  String get friendlyMessage => ErrorHandler.parseError(this);
  bool get isUnauthorized => ErrorHandler.isUnauthorized(this);
  bool get isNetworkError => ErrorHandler.isNetworkError(this);
  bool get isServerError => ErrorHandler.isServerError(this);
}
