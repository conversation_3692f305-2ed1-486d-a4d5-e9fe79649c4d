# Jalankan dari root folder project Flutter
Write-Host "📁 Membuat struktur folder Flutter (Clean Architecture)..."

$folders = @(
  "lib/core/theme",
  "lib/core/network",
  "lib/core/storage",
  "lib/data/models",
  "lib/data/services",
  "lib/domain/entities",
  "lib/domain/usecases",
  "lib/presentation/pages",
  "lib/presentation/providers",
  "lib/routes"
)

foreach ($folder in $folders) {
  if (-not (Test-Path $folder)) {
    New-Item -ItemType Directory -Path $folder | Out-Null
    Write-Host "✅  Created: $folder"
  } else {
    Write-Host "⚠️  Already exists: $folder"
  }
}

Write-Host "`n✅ Struktur folder selesai dibuat!"
