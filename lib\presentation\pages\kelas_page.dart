import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/presentation/providers/kelas_provider.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';

class KelasPage extends ConsumerStatefulWidget {
  const KelasPage({super.key});

  @override
  ConsumerState<KelasPage> createState() => _KelasPageState();
}

class _KelasPageState extends ConsumerState<KelasPage> {
  @override
  void initState() {
    super.initState();
    // Auto-fetch saat halaman pertama muncul
    Future.microtask(() {
      final user = ref.read(authProvider).user;
      if (user != null && user.komplekId != null) {
        ref.read(kelasProvider.notifier).fetch(user.komplekId!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final kelasState = ref.watch(kelasProvider);
    final user = ref.read(authProvider).user;
    final komplekId = user?.komplekId;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Kelola Kelas'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (komplekId != null) {
                ref.read(kelasProvider.notifier).fetch(komplekId);
              }
            },
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: kelasState.isSaving
            ? null
            : () {
                if (komplekId != null) {
                  _showCreateOrEditDialog(komplekId: komplekId);
                }
              },
        child: const Icon(Icons.add),
      ),
      body: kelasState.data.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, _) => Center(child: Text('Error: $e')),
        data: (data) {
          if (data.isEmpty) {
            return const Center(child: Text('Belum ada kelas.'));
          }
          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: data.length,
            separatorBuilder: (_, __) => const Divider(),
            itemBuilder: (context, index) {
              final kelas = data[index];
              return ListTile(
                leading: const Icon(Icons.class_),
                title: Text(kelas.namaKelas),
                subtitle: Text('ID Komplek: ${kelas.idKomplek}'),
                trailing: PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showCreateOrEditDialog(
                        komplekId: kelas.idKomplek,
                        kelas: kelas,
                      );
                    } else if (value == 'delete') {
                      _confirmDelete(kelas.idKomplek, kelas.id);
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Text('Edit'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Hapus'),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  void _showCreateOrEditDialog({
    required int komplekId,
    kelas,
  }) {
    final formKey = GlobalKey<FormState>();
    String namaKelas = kelas?.namaKelas ?? '';

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            final saving = ref.watch(kelasProvider).isSaving;
            final errorMessage = ref.watch(kelasProvider).errorMessage;
            return AlertDialog(
              title: Text(kelas == null ? 'Tambah Kelas' : 'Edit Kelas'),
              content: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      initialValue: namaKelas,
                      decoration: const InputDecoration(labelText: 'Nama Kelas'),
                      onChanged: (v) => namaKelas = v,
                      validator: (v) =>
                          v == null || v.isEmpty ? 'Wajib diisi' : null,
                    ),
                    if (errorMessage != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        errorMessage,
                        style: const TextStyle(color: Colors.red),
                      ),
                    ],
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: saving ? null : () => Navigator.pop(context),
                  child: const Text('Batal'),
                ),
                ElevatedButton(
                  onPressed: saving
                      ? null
                      : () async {
                          if (!formKey.currentState!.validate()) return;
                          final payload = {
                            'nama_kelas': namaKelas,
                          };

                          if (kelas == null) {
                            await ref
                                .read(kelasProvider.notifier)
                                .create(komplekId, payload);
                          } else {
                            await ref
                                .read(kelasProvider.notifier)
                                .update(komplekId, kelas.id, payload);
                          }

                          if (mounted) Navigator.pop(context);
                        },
                  child: saving
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Simpan'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _confirmDelete(int komplekId, int id) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Kelas'),
        content: const Text('Yakin ingin menghapus kelas ini?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      await ref.read(kelasProvider.notifier).delete(komplekId, id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Kelas dihapus.')),
        );
      }
    }
  }
}
