import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/presentation/providers/kelas_provider.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

class KelasPage extends ConsumerStatefulWidget {
  const KelasPage({super.key});

  @override
  ConsumerState<KelasPage> createState() => _KelasPageState();
}

class _KelasPageState extends ConsumerState<KelasPage> {
  @override
  void initState() {
    super.initState();
    // Auto-fetch saat halaman pertama muncul
    Future.microtask(() {
      final user = ref.read(authProvider).user;
      if (user != null && user.komplekId != null) {
        ref.read(kelasProvider.notifier).fetch(user.komplekId!);
      }
    });
  }

  /// Helper method untuk show error snackbar
  void showErrorSnackBar(dynamic error) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  error.toString(),
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final kelasState = ref.watch(kelasProvider);
    final user = ref.watch(authProvider).user;
    final theme = Theme.of(context);
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header Section
          _buildHeader(theme, kelasState, user),

          // Content
          Expanded(
            child: kelasState.data.when(
              loading: () => _buildLoadingState(theme),
              error: (e, _) => _buildErrorState(e, user),
              data: (data) {
                if (data.isEmpty) {
                  return _buildEmptyState(theme, kelasState);
                }
                return _buildKelasList(theme, data, isDesktop);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(theme, kelasState),
    );
  }

  /// Build header section dengan search dan refresh
  Widget _buildHeader(ThemeData theme, KelasState kelasState, dynamic user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.class_,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Kelola Kelas',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Kelola data kelas di komplek Anda',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton.filledTonal(
                onPressed: () {
                  if (user != null && user.komplekId != null) {
                    ref.read(kelasProvider.notifier).fetch(user.komplekId!);
                  }
                },
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh Data',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Memuat data kelas...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(dynamic error, dynamic user) {
    return ErrorStateWidget(
      error: error,
      onRetry: () {
        if (user != null && user.komplekId != null) {
          ref.read(kelasProvider.notifier).fetch(user.komplekId!);
        }
      },
    );
  }

  /// Build empty state
  Widget _buildEmptyState(ThemeData theme, KelasState kelasState) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon dengan gradient background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      theme.colorScheme.secondary.withValues(alpha: 0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.class_outlined,
                  size: 60,
                  color: theme.colorScheme.primary,
                ),
              ),

              const SizedBox(height: 32),

              // Title
              Text(
                'Belum Ada Kelas',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Subtitle
              Text(
                'Belum ada kelas yang terdaftar di komplek ini.\nTambahkan kelas baru untuk memulai.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // Error message jika ada
              if (kelasState.errorMessage != null) ...[
                CompactErrorWidget(
                  error: kelasState.errorMessage!,
                ),
                const SizedBox(height: 24),
              ],

              // Add button
              FilledButton.icon(
                onPressed: kelasState.isSaving ? null : () => _showCreateOrEditDialog(),
                icon: kelasState.isSaving
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.onPrimary,
                        ),
                      )
                    : const Icon(Icons.add),
                label: Text(kelasState.isSaving ? 'Menambahkan...' : 'Tambah Kelas'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  textStyle: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build kelas list dengan design yang modern
  Widget _buildKelasList(ThemeData theme, List<dynamic> data, bool isDesktop) {
    return Column(
      children: [
        // Stats header
        Container(
          width: double.infinity,
          margin: EdgeInsets.all(isDesktop ? 24 : 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.class_,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '${data.length} Kelas Terdaftar',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),

        // List
        Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: isDesktop ? 24 : 16),
            child: isDesktop ? _buildDesktopGrid(theme, data) : _buildMobileList(theme, data),
          ),
        ),

        // Bottom padding
        SizedBox(height: isDesktop ? 24 : 16),
      ],
    );
  }

  /// Build desktop grid layout
  Widget _buildDesktopGrid(ThemeData theme, List<dynamic> data) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Responsive grid columns based on screen width
        int crossAxisCount = 1;
        if (constraints.maxWidth > 1200) {
          crossAxisCount = 4;
        } else if (constraints.maxWidth > 900) {
          crossAxisCount = 3;
        } else if (constraints.maxWidth > 600) {
          crossAxisCount = 2;
        }

        return GridView.builder(
          padding: const EdgeInsets.only(bottom: 16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2, // Square-ish cards for classes
          ),
          itemCount: data.length,
          itemBuilder: (context, index) {
            final kelas = data[index];
            return _buildKelasCard(theme, kelas, isDesktop: true);
          },
        );
      },
    );
  }

  /// Build mobile list layout
  Widget _buildMobileList(ThemeData theme, List<dynamic> data) {
    return ListView.separated(
      padding: const EdgeInsets.only(bottom: 16),
      itemCount: data.length,
      separatorBuilder: (_, __) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final kelas = data[index];
        return _buildKelasCard(theme, kelas, isDesktop: false);
      },
    );
  }

  /// Build kelas card
  Widget _buildKelasCard(ThemeData theme, dynamic kelas, {required bool isDesktop}) {
    return Card(
      elevation: 0,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showCreateOrEditDialog(komplekId: kelas.idKomplek, kelas: kelas),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header row dengan icon dan actions
              Row(
                children: [
                  // Icon
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.class_,
                      color: theme.colorScheme.onPrimaryContainer,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // Class name
                  Expanded(
                    child: Text(
                      kelas.namaKelas,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // Actions menu
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'edit') {
                        _showCreateOrEditDialog(komplekId: kelas.idKomplek, kelas: kelas);
                      } else if (value == 'delete') {
                        _confirmDelete(kelas.idKomplek, kelas.id);
                      }
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.edit, size: 16, color: theme.colorScheme.primary),
                            const SizedBox(width: 8),
                            const Text('Edit'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.delete, size: 16, color: theme.colorScheme.error),
                            const SizedBox(width: 8),
                            const Text('Hapus'),
                          ],
                        ),
                      ),
                    ],
                    icon: Icon(
                      Icons.more_vert,
                      color: theme.colorScheme.onSurfaceVariant,
                      size: 18,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Class info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Komplek ID: ${kelas.idKomplek}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton(ThemeData theme, KelasState kelasState) {
    final user = ref.read(authProvider).user;
    final komplekId = user?.komplekId;

    return FloatingActionButton.extended(
      onPressed: kelasState.isSaving || komplekId == null
          ? null
          : () => _showCreateOrEditDialog(komplekId: komplekId),
      icon: kelasState.isSaving
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: theme.colorScheme.onPrimary,
              ),
            )
          : const Icon(Icons.add),
      label: Text(kelasState.isSaving ? 'Menambahkan...' : 'Tambah Kelas'),
    );
  }

  void _showCreateOrEditDialog({int? komplekId, kelas}) {
    final theme = Theme.of(context);
    final user = ref.read(authProvider).user;
    final actualKomplekId = komplekId ?? user?.komplekId;

    if (actualKomplekId == null) {
      showErrorSnackBar('ID Komplek tidak ditemukan. Silakan login ulang.');
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        final formKey = GlobalKey<FormState>();
        String namaKelas = kelas?.namaKelas ?? '';

        return StatefulBuilder(
          builder: (context, setState) {
            final saving = ref.watch(kelasProvider).isSaving;

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            kelas == null ? Icons.add : Icons.edit,
                            color: theme.colorScheme.onPrimaryContainer,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                kelas == null ? 'Tambah Kelas' : 'Edit Kelas',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                kelas == null
                                    ? 'Tambahkan kelas baru ke komplek'
                                    : 'Ubah informasi kelas',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Error message
                    if (ref.watch(kelasProvider).errorMessage != null) ...[
                      CompactErrorWidget(
                        error: ref.watch(kelasProvider).errorMessage!,
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Form
                    Form(
                      key: formKey,
                      child: TextFormField(
                        initialValue: namaKelas,
                        decoration: InputDecoration(
                          labelText: 'Nama Kelas',
                          hintText: 'Masukkan nama kelas',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.class_outlined),
                        ),
                        onChanged: (v) => namaKelas = v,
                        validator: (v) => v == null || v.isEmpty ? 'Nama kelas wajib diisi' : null,
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Actions
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: saving ? null : () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Text('Batal'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 2,
                          child: FilledButton.icon(
                            onPressed: saving
                                ? null
                                : () async {
                                    if (!formKey.currentState!.validate()) return;

                                    final payload = {
                                      'nama_kelas': namaKelas,
                                    };

                                    final navigator = Navigator.of(context);
                                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                                    try {
                                      if (kelas == null) {
                                        await ref.read(kelasProvider.notifier).create(actualKomplekId, payload);
                                      } else {
                                        await ref.read(kelasProvider.notifier).update(actualKomplekId, kelas.id, payload);
                                      }

                                      if (mounted) {
                                        navigator.pop();
                                        scaffoldMessenger.showSnackBar(
                                          SnackBar(
                                            content: Row(
                                              children: [
                                                const Icon(Icons.check_circle, color: Colors.white),
                                                const SizedBox(width: 12),
                                                Text(kelas == null
                                                    ? 'Kelas berhasil ditambahkan'
                                                    : 'Kelas berhasil diperbarui'),
                                              ],
                                            ),
                                            backgroundColor: Colors.green,
                                            behavior: SnackBarBehavior.floating,
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (mounted) {
                                        showErrorSnackBar(e);
                                      }
                                    }
                                  },
                            icon: saving
                                ? SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: theme.colorScheme.onPrimary,
                                    ),
                                  )
                                : const Icon(Icons.save),
                            label: Text(saving ? 'Menyimpan...' : 'Simpan'),
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _confirmDelete(int komplekId, int id) async {
    final theme = Theme.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: theme.colorScheme.error,
            ),
            const SizedBox(width: 12),
            const Text('Hapus Kelas'),
          ],
        ),
        content: const Text(
          'Yakin ingin menghapus kelas ini? Tindakan ini tidak dapat dibatalkan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
            ),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await ref.read(kelasProvider.notifier).delete(komplekId, id);
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.delete, color: Colors.white),
                  SizedBox(width: 12),
                  Text('Kelas berhasil dihapus'),
                ],
              ),
              backgroundColor: theme.colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar(e);
        }
      }
    }
  }
}
