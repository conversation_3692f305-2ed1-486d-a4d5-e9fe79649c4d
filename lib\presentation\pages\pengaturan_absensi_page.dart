import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';
import 'package:sistem_pondok_apps/presentation/providers/pengaturan_tahfidz_provider.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

class PengaturanAbsensiPage extends ConsumerStatefulWidget {
  const PengaturanAbsensiPage({super.key});

  @override
  ConsumerState<PengaturanAbsensiPage> createState() => _PengaturanAbsensiPageState();
}

class _PengaturanAbsensiPageState extends ConsumerState<PengaturanAbsensiPage> {
  final _formKey = GlobalKey<FormState>();
  String _mode = 'halaman';
  DateTime _berlakuMulai = DateTime.now();
  bool _aktif = true;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Gunakan WidgetsBinding untuk memastikan widget sudah ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = ref.read(authProvider).user;
      if (user != null && user.komplekId != null) {
        ref.read(pengaturanTahfidzProvider.notifier).fetch(user.komplekId!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final pengaturanState = ref.watch(pengaturanTahfidzProvider);
    final user = ref.read(authProvider).user;

    return pengaturanState.data.when(
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (e, _) => ErrorStateWidget(
          error: e,
          onRetry: () {
            if (user != null && user.komplekId != null) {
              ref.read(pengaturanTahfidzProvider.notifier).fetch(user.komplekId!);
            }
          },
        ),
        data: (data) {
          if (data == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text('Belum ada pengaturan aktif untuk komplek ini.'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: pengaturanState.isSaving
                        ? null
                        : () {
                            _showCreateDialog();
                          },
                    child: pengaturanState.isSaving
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Buat Pengaturan Absensi Tahfidz'),
                  ),
                  if (pengaturanState.errorMessage != null) ...[
                    const SizedBox(height: 8),
                    CompactErrorWidget(
                      error: pengaturanState.errorMessage!,
                    ),
                  ],
                ],
              ),
            );
          }

          // Inisialisasi form edit hanya sekali
          if (!_isInitialized) {
            _mode = data.mode;
            _berlakuMulai = data.berlakuMulai;
            _aktif = data.aktif;
            _isInitialized = true;
          }

          return Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  DropdownButtonFormField<String>(
                    value: _mode,
                    items: const [
                      DropdownMenuItem(value: 'halaman', child: Text('Halaman')),
                      DropdownMenuItem(value: 'ayat', child: Text('Ayat')),
                    ],
                    onChanged: (v) {
                      setState(() {
                        _mode = v!;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: 'Mode Setoran',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    readOnly: true,
                    controller: TextEditingController(
                      text: DateFormat('yyyy-MM-dd').format(_berlakuMulai),
                    ),
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: _berlakuMulai,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() {
                          _berlakuMulai = picked;
                        });
                      }
                    },
                    decoration: const InputDecoration(
                      labelText: 'Berlaku Mulai',
                    ),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    value: _aktif,
                    onChanged: (v) {
                      setState(() {
                        _aktif = v;
                      });
                    },
                    title: const Text('Aktif'),
                  ),
                  if (pengaturanState.errorMessage != null) ...[
                    const SizedBox(height: 8),
                    CompactErrorWidget(
                      error: pengaturanState.errorMessage!,
                    ),
                  ],
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: pengaturanState.isSaving
                        ? null
                        : () async {
                            if (user == null || user.komplekId == null) return;

                            final payload = {
                              'mode': _mode,
                              'berlaku_mulai': DateFormat('yyyy-MM-dd').format(_berlakuMulai),
                              'aktif': _aktif,
                              'id_komplek': user.komplekId,
                            };

                            final scaffoldMessenger = ScaffoldMessenger.of(context);
                            await ref
                                .read(pengaturanTahfidzProvider.notifier)
                                .update(data.id, payload);

                            if (mounted) {
                              scaffoldMessenger.showSnackBar(
                                const SnackBar(content: Text('Pengaturan berhasil diperbarui.')),
                              );
                            }
                          },
                    child: pengaturanState.isSaving
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Simpan Perubahan'),
                  ),
                  const SizedBox(height: 16),
                  OutlinedButton(
                    onPressed: pengaturanState.isSaving
                        ? null
                        : () async {
                            final scaffoldMessenger = ScaffoldMessenger.of(context);
                            final confirm = await showDialog<bool>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Hapus Pengaturan'),
                                content: const Text('Yakin ingin menghapus pengaturan ini?'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context, false),
                                    child: const Text('Batal'),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.pop(context, true),
                                    child: const Text('Hapus'),
                                  ),
                                ],
                              ),
                            );

                            if (confirm == true) {
                              await ref
                                  .read(pengaturanTahfidzProvider.notifier)
                                  .delete(data.id, user!.komplekId!);

                              if (mounted) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(content: Text('Pengaturan dihapus.')),
                                );
                              }
                            }
                          },
                    child: const Text('Hapus Pengaturan'),
                  ),
                ],
              ),
            ),
          );
        },
      );
  }

  void _showCreateDialog() {
    showDialog(
      context: context,
      builder: (context) {
        String mode = 'halaman';
        DateTime berlakuMulai = DateTime.now();
        bool aktif = true;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Buat Pengaturan Baru'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  DropdownButtonFormField<String>(
                    value: mode,
                    items: const [
                      DropdownMenuItem(value: 'halaman', child: Text('Halaman')),
                      DropdownMenuItem(value: 'ayat', child: Text('Ayat')),
                    ],
                    onChanged: (v) {
                      setState(() {
                        mode = v!;
                      });
                    },
                    decoration: const InputDecoration(
                      labelText: 'Mode Setoran',
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextFormField(
                    readOnly: true,
                    controller: TextEditingController(
                      text: DateFormat('yyyy-MM-dd').format(berlakuMulai),
                    ),
                    onTap: () async {
                      final picked = await showDatePicker(
                        context: context,
                        initialDate: berlakuMulai,
                        firstDate: DateTime(2020),
                        lastDate: DateTime(2100),
                      );
                      if (picked != null) {
                        setState(() {
                          berlakuMulai = picked;
                        });
                      }
                    },
                    decoration: const InputDecoration(
                      labelText: 'Berlaku Mulai',
                    ),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    value: aktif,
                    onChanged: (v) {
                      setState(() {
                        aktif = v;
                      });
                    },
                    title: const Text('Aktif'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Batal'),
                ),
                Consumer(
                  builder: (context, ref, _) {
                    final saving = ref.watch(pengaturanTahfidzProvider).isSaving;
                    return ElevatedButton(
                      onPressed: saving
                          ? null
                          : () async {
                              final user = ref.read(authProvider).user;
                              final navigator = Navigator.of(context);
                              final scaffoldMessenger = ScaffoldMessenger.of(context);

                              if (user == null || user.komplekId == null) {
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                      content: Text('User atau komplek tidak valid.')),
                                );
                                return;
                              }

                              final payload = {
                                'mode': mode,
                                'berlaku_mulai':
                                    DateFormat('yyyy-MM-dd').format(berlakuMulai),
                                'aktif': aktif,
                                'id_komplek': user.komplekId,
                              };

                              await ref
                                  .read(pengaturanTahfidzProvider.notifier)
                                  .create(payload);

                              if (mounted) navigator.pop();
                            },
                      child: saving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Text('Simpan'),
                    );
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }
}
