import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';
import 'package:sistem_pondok_apps/presentation/providers/pengaturan_tahfidz_provider.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

class PengaturanAbsensiPage extends ConsumerStatefulWidget {
  const PengaturanAbsensiPage({super.key});

  @override
  ConsumerState<PengaturanAbsensiPage> createState() => _PengaturanAbsensiPageState();
}

class _PengaturanAbsensiPageState extends ConsumerState<PengaturanAbsensiPage> {
  final _formKey = GlobalKey<FormState>();
  String _mode = 'halaman';
  DateTime _berlakuMulai = DateTime.now();
  bool _aktif = true;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    // Gunakan WidgetsBinding untuk memastikan widget sudah ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = ref.read(authProvider).user;
      if (user != null && user.komplekId != null) {
        ref.read(pengaturanTahfidzProvider.notifier).fetch(user.komplekId!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final pengaturanState = ref.watch(pengaturanTahfidzProvider);
    final user = ref.read(authProvider).user;
    final theme = Theme.of(context);
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: pengaturanState.data.when(
        loading: () => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Memuat pengaturan...',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        error: (e, _) => ErrorStateWidget(
          error: e,
          onRetry: () {
            if (user != null && user.komplekId != null) {
              ref.read(pengaturanTahfidzProvider.notifier).fetch(user.komplekId!);
            }
          },
        ),
        data: (data) {
          if (data == null) {
            return _buildEmptyState(context, theme, pengaturanState);
          }

          return _buildMainContent(context, theme, data, pengaturanState, user, isDesktop);
        },
      ),
    );
  }

  /// Build empty state dengan design yang menarik
  Widget _buildEmptyState(BuildContext context, ThemeData theme, PengaturanTahfidzState pengaturanState) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon dengan gradient background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      theme.colorScheme.secondary.withValues(alpha: 0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.settings_outlined,
                  size: 60,
                  color: theme.colorScheme.primary,
                ),
              ),

              const SizedBox(height: 32),

              // Title
              Text(
                'Belum Ada Pengaturan',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Subtitle
              Text(
                'Belum ada pengaturan absensi tahfidz untuk komplek ini.\nBuat pengaturan baru untuk memulai.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // Error message jika ada
              if (pengaturanState.errorMessage != null) ...[
                CompactErrorWidget(
                  error: pengaturanState.errorMessage!,
                ),
                const SizedBox(height: 24),
              ],

              // Create button
              FilledButton.icon(
                onPressed: pengaturanState.isSaving ? null : () => _showCreateDialog(),
                icon: pengaturanState.isSaving
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.onPrimary,
                        ),
                      )
                    : const Icon(Icons.add),
                label: Text(pengaturanState.isSaving ? 'Membuat...' : 'Buat Pengaturan'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  textStyle: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build main content dengan design yang modern dan responsive
  Widget _buildMainContent(
    BuildContext context,
    ThemeData theme,
    dynamic data,
    PengaturanTahfidzState pengaturanState,
    dynamic user,
    bool isDesktop,
  ) {
    // Inisialisasi form edit hanya sekali
    if (!_isInitialized) {
      _mode = data.mode;
      _berlakuMulai = data.berlakuMulai;
      _aktif = data.aktif;
      _isInitialized = true;
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(isDesktop ? 32 : 16),
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: isDesktop ? 800 : double.infinity),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              _buildHeader(theme),

              const SizedBox(height: 32),

              // Main Card
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                  side: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Form Title
                        Text(
                          'Pengaturan Absensi',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Form Fields
                        _buildFormFields(theme),

                        const SizedBox(height: 32),

                        // Error Message
                        if (pengaturanState.errorMessage != null) ...[
                          CompactErrorWidget(
                            error: pengaturanState.errorMessage!,
                          ),
                          const SizedBox(height: 24),
                        ],

                        // Action Buttons
                        _buildActionButtons(theme, pengaturanState, data, user),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header section
  Widget _buildHeader(ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.settings,
            color: theme.colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pengaturan Absensi Tahfidz',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Kelola pengaturan absensi tahfidz untuk komplek Anda',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build form fields dengan design yang modern
  Widget _buildFormFields(ThemeData theme) {
    return Column(
      children: [
        // Mode Setoran Field
        _buildFieldContainer(
          theme: theme,
          title: 'Mode Setoran',
          subtitle: 'Pilih mode setoran yang akan digunakan',
          icon: Icons.book_outlined,
          child: DropdownButtonFormField<String>(
            value: _mode,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: const [
              DropdownMenuItem(
                value: 'halaman',
                child: Row(
                  children: [
                    Icon(Icons.menu_book, size: 20),
                    SizedBox(width: 12),
                    Text('Halaman'),
                  ],
                ),
              ),
              DropdownMenuItem(
                value: 'ayat',
                child: Row(
                  children: [
                    Icon(Icons.format_list_numbered, size: 20),
                    SizedBox(width: 12),
                    Text('Ayat'),
                  ],
                ),
              ),
            ],
            onChanged: (v) {
              setState(() {
                _mode = v!;
              });
            },
          ),
        ),

        const SizedBox(height: 24),

        // Berlaku Mulai Field
        _buildFieldContainer(
          theme: theme,
          title: 'Berlaku Mulai',
          subtitle: 'Tanggal mulai berlakunya pengaturan ini',
          icon: Icons.calendar_today_outlined,
          child: TextFormField(
            readOnly: true,
            controller: TextEditingController(
              text: DateFormat('dd MMMM yyyy', 'id_ID').format(_berlakuMulai),
            ),
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
              ),
              filled: true,
              fillColor: theme.colorScheme.surface,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              suffixIcon: Icon(
                Icons.calendar_today,
                color: theme.colorScheme.primary,
              ),
            ),
            onTap: () async {
              final picked = await showDatePicker(
                context: context,
                initialDate: _berlakuMulai,
                firstDate: DateTime(2020),
                lastDate: DateTime(2100),
                builder: (context, child) {
                  return Theme(
                    data: theme.copyWith(
                      colorScheme: theme.colorScheme.copyWith(
                        primary: theme.colorScheme.primary,
                      ),
                    ),
                    child: child!,
                  );
                },
              );
              if (picked != null) {
                setState(() {
                  _berlakuMulai = picked;
                });
              }
            },
          ),
        ),

        const SizedBox(height: 24),

        // Status Aktif Field
        _buildFieldContainer(
          theme: theme,
          title: 'Status',
          subtitle: 'Aktifkan atau nonaktifkan pengaturan ini',
          icon: Icons.toggle_on_outlined,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
              borderRadius: BorderRadius.circular(12),
              color: theme.colorScheme.surface,
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Pengaturan Aktif',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _aktif ? 'Pengaturan sedang aktif' : 'Pengaturan tidak aktif',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: _aktif,
                  onChanged: (v) {
                    setState(() {
                      _aktif = v;
                    });
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build field container dengan header yang konsisten
  Widget _buildFieldContainer({
    required ThemeData theme,
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(ThemeData theme, PengaturanTahfidzState pengaturanState, dynamic data, dynamic user) {
    return Column(
      children: [
        // Save Button
        SizedBox(
          width: double.infinity,
          child: FilledButton.icon(
            onPressed: pengaturanState.isSaving
                ? null
                : () async {
                    if (user == null || user.komplekId == null) return;

                    final payload = {
                      'mode': _mode,
                      'berlaku_mulai': DateFormat('yyyy-MM-dd').format(_berlakuMulai),
                      'aktif': _aktif,
                      'id_komplek': user.komplekId,
                    };

                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    await ref
                        .read(pengaturanTahfidzProvider.notifier)
                        .update(data.id, payload);

                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: const Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.white),
                              SizedBox(width: 12),
                              Text('Pengaturan berhasil diperbarui'),
                            ],
                          ),
                          backgroundColor: Colors.green,
                          behavior: SnackBarBehavior.floating,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      );
                    }
                  },
            icon: pengaturanState.isSaving
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: theme.colorScheme.onPrimary,
                    ),
                  )
                : const Icon(Icons.save),
            label: Text(pengaturanState.isSaving ? 'Menyimpan...' : 'Simpan Perubahan'),
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Delete Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: pengaturanState.isSaving ? null : () => _showDeleteConfirmation(data, user),
            icon: const Icon(Icons.delete_outline),
            label: const Text('Hapus Pengaturan'),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.error,
              side: BorderSide(color: theme.colorScheme.error.withValues(alpha: 0.5)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              textStyle: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmation(dynamic data, dynamic user) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final theme = Theme.of(context);

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: theme.colorScheme.error,
            ),
            const SizedBox(width: 12),
            const Text('Hapus Pengaturan'),
          ],
        ),
        content: const Text(
          'Yakin ingin menghapus pengaturan ini? Tindakan ini tidak dapat dibatalkan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
            ),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      await ref
          .read(pengaturanTahfidzProvider.notifier)
          .delete(data.id, user!.komplekId!);

      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: const Row(
              children: [
                Icon(Icons.delete, color: Colors.white),
                SizedBox(width: 12),
                Text('Pengaturan berhasil dihapus'),
              ],
            ),
            backgroundColor: theme.colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    }
  }

  void _showCreateDialog() {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) {
        String mode = 'halaman';
        DateTime berlakuMulai = DateTime.now();
        bool aktif = true;

        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.add,
                            color: theme.colorScheme.onPrimaryContainer,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Buat Pengaturan Baru',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Atur pengaturan absensi tahfidz',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Form Fields
                    DropdownButtonFormField<String>(
                      value: mode,
                      decoration: InputDecoration(
                        labelText: 'Mode Setoran',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.book_outlined),
                      ),
                      items: const [
                        DropdownMenuItem(
                          value: 'halaman',
                          child: Text('Halaman'),
                        ),
                        DropdownMenuItem(
                          value: 'ayat',
                          child: Text('Ayat'),
                        ),
                      ],
                      onChanged: (v) {
                        setState(() {
                          mode = v!;
                        });
                      },
                    ),

                    const SizedBox(height: 16),

                    TextFormField(
                      readOnly: true,
                      controller: TextEditingController(
                        text: DateFormat('dd MMMM yyyy', 'id_ID').format(berlakuMulai),
                      ),
                      decoration: InputDecoration(
                        labelText: 'Berlaku Mulai',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.calendar_today_outlined),
                        suffixIcon: const Icon(Icons.arrow_drop_down),
                      ),
                      onTap: () async {
                        final picked = await showDatePicker(
                          context: context,
                          initialDate: berlakuMulai,
                          firstDate: DateTime(2020),
                          lastDate: DateTime(2100),
                          builder: (context, child) {
                            return Theme(
                              data: theme.copyWith(
                                colorScheme: theme.colorScheme.copyWith(
                                  primary: theme.colorScheme.primary,
                                ),
                              ),
                              child: child!,
                            );
                          },
                        );
                        if (picked != null) {
                          setState(() {
                            berlakuMulai = picked;
                          });
                        }
                      },
                    ),

                    const SizedBox(height: 16),

                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.5)),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.toggle_on_outlined,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Status Aktif',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Text(
                                  aktif ? 'Pengaturan akan aktif' : 'Pengaturan tidak aktif',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: theme.colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: aktif,
                            onChanged: (v) {
                              setState(() {
                                aktif = v;
                              });
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Actions
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Text('Batal'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 2,
                          child: Consumer(
                            builder: (context, ref, _) {
                              final saving = ref.watch(pengaturanTahfidzProvider).isSaving;
                              return FilledButton.icon(
                                onPressed: saving
                                    ? null
                                    : () async {
                                        final user = ref.read(authProvider).user;
                                        final navigator = Navigator.of(context);
                                        final scaffoldMessenger = ScaffoldMessenger.of(context);

                                        if (user == null || user.komplekId == null) {
                                          scaffoldMessenger.showSnackBar(
                                            SnackBar(
                                              content: const Row(
                                                children: [
                                                  Icon(Icons.error, color: Colors.white),
                                                  SizedBox(width: 12),
                                                  Text('User atau komplek tidak valid'),
                                                ],
                                              ),
                                              backgroundColor: theme.colorScheme.error,
                                              behavior: SnackBarBehavior.floating,
                                            ),
                                          );
                                          return;
                                        }

                                        final payload = {
                                          'mode': mode,
                                          'berlaku_mulai': DateFormat('yyyy-MM-dd').format(berlakuMulai),
                                          'aktif': aktif,
                                          'id_komplek': user.komplekId,
                                        };

                                        await ref
                                            .read(pengaturanTahfidzProvider.notifier)
                                            .create(payload);

                                        if (mounted) {
                                          navigator.pop();
                                          scaffoldMessenger.showSnackBar(
                                            SnackBar(
                                              content: const Row(
                                                children: [
                                                  Icon(Icons.check_circle, color: Colors.white),
                                                  SizedBox(width: 12),
                                                  Text('Pengaturan berhasil dibuat'),
                                                ],
                                              ),
                                              backgroundColor: Colors.green,
                                              behavior: SnackBarBehavior.floating,
                                            ),
                                          );
                                        }
                                      },
                                icon: saving
                                    ? SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      )
                                    : const Icon(Icons.save),
                                label: Text(saving ? 'Menyimpan...' : 'Simpan'),
                                style: FilledButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
