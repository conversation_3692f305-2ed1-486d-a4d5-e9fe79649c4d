import 'package:flutter/material.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';

/// Widget untuk menampilkan error state yang user-friendly
class ErrorStateWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;
  final String? customMessage;
  final IconData? customIcon;

  const ErrorStateWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
    this.customIcon,
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = customMessage ?? ErrorHandler.parseError(error);
    final isNetworkError = ErrorHandler.isNetworkError(error);
    final isServerError = ErrorHandler.isServerError(error);
    
    IconData icon;
    Color iconColor;
    
    if (customIcon != null) {
      icon = customIcon!;
      iconColor = Colors.grey;
    } else if (isNetworkError) {
      icon = Icons.wifi_off;
      iconColor = Colors.orange;
    } else if (isServerError) {
      icon = Icons.error_outline;
      iconColor = Colors.red;
    } else {
      icon = Icons.warning_amber_outlined;
      iconColor = Colors.amber;
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: iconColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Oops!',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Coba Lagi'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Widget untuk error state yang compact (untuk digunakan di dalam card, dll)
class CompactErrorWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onRetry;
  final String? customMessage;

  const CompactErrorWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = customMessage ?? ErrorHandler.parseError(error);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              errorMessage,
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 14,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: Colors.red.shade600,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }
}

/// Widget untuk menampilkan error dalam bentuk banner
class ErrorBannerWidget extends StatelessWidget {
  final dynamic error;
  final VoidCallback? onDismiss;
  final VoidCallback? onRetry;
  final String? customMessage;

  const ErrorBannerWidget({
    super.key,
    required this.error,
    this.onDismiss,
    this.onRetry,
    this.customMessage,
  });

  @override
  Widget build(BuildContext context) {
    final errorMessage = customMessage ?? ErrorHandler.parseError(error);
    
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        border: Border(
          left: BorderSide(
            color: Colors.red.shade600,
            width: 4,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              errorMessage,
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 14,
              ),
            ),
          ),
          if (onRetry != null) ...[
            TextButton(
              onPressed: onRetry,
              style: TextButton.styleFrom(
                foregroundColor: Colors.red.shade600,
                padding: const EdgeInsets.symmetric(horizontal: 8),
                minimumSize: const Size(0, 32),
              ),
              child: const Text('Retry'),
            ),
          ],
          if (onDismiss != null) ...[
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: Colors.red.shade600,
                size: 18,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Mixin untuk memudahkan penggunaan error handling di StatefulWidget
mixin ErrorHandlerMixin<T extends StatefulWidget> on State<T> {
  void showErrorDialog(dynamic error, {String? title}) {
    ErrorHandler.showErrorDialog(context, error, title: title);
  }

  void showErrorSnackBar(dynamic error) {
    ErrorHandler.showErrorSnackBar(context, error);
  }

  Widget buildErrorState(dynamic error, {VoidCallback? onRetry}) {
    return ErrorStateWidget(
      error: error,
      onRetry: onRetry,
    );
  }

  Widget buildCompactError(dynamic error, {VoidCallback? onRetry}) {
    return CompactErrorWidget(
      error: error,
      onRetry: onRetry,
    );
  }
}
