import 'package:sistem_pondok_apps/core/network/api_client.dart';
import 'package:sistem_pondok_apps/domain/entities/ustadz_entity.dart';

class UstadzService {
  final _api = ApiClient();

  Future<List<UstadzEntity>> getAll({String? search, int? idKomplek}) async {
    final response = await _api.dio.get('/ustadz', queryParameters: {
      if (search != null) 'search': search,
      if (idKomplek != null) 'id_komplek': idKomplek,
    });

    // Handle API response structure
    if (response.data['success'] == true) {
      final data = response.data['data'] as List;
      return data.map((e) => UstadzEntity.fromJson(e)).toList();
    } else {
      throw Exception(response.data['message'] ?? 'Failed to fetch ustadz data');
    }
  }

  Future<UstadzEntity> getDetail(int id) async {
    final response = await _api.dio.get('/ustadz/$id');

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to fetch ustadz detail');
    }
  }

  Future<UstadzEntity> create(Map<String, dynamic> payload) async {
    final response = await _api.dio.post('/ustadz', data: payload);

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to create ustadz');
    }
  }

  Future<UstadzEntity> update(int id, Map<String, dynamic> payload) async {
    final response = await _api.dio.put('/ustadz/$id', data: payload);

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to update ustadz');
    }
  }

  Future<void> delete(int id) async {
    final response = await _api.dio.delete('/ustadz/$id');

    if (response.data['success'] != true) {
      throw Exception(response.data['message'] ?? 'Failed to delete ustadz');
    }
  }
}
