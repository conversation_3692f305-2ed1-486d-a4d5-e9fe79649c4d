import 'package:sistem_pondok_apps/core/network/api_client.dart';
import 'package:sistem_pondok_apps/domain/entities/ustadz_entity.dart';

class UstadzService {
  final _api = ApiClient();

  Future<List<UstadzEntity>> getAll({String? search, int? idKomplek}) async {
    try {
      print('🔍 Fetching ustadz with params: search=$search, idKomplek=$idKomplek');

      final response = await _api.dio.get('/ustadz', queryParameters: {
        if (search != null) 'search': search,
        if (idKomplek != null) 'id_komplek': idKomplek,
      });

      print('📡 API Response: ${response.data}');

      // Handle API response structure
      if (response.data['success'] == true) {
        final data = response.data['data'] as List;
        print('📊 Found ${data.length} ustadz records');

        final entities = <UstadzEntity>[];
        for (int i = 0; i < data.length; i++) {
          try {
            final entity = UstadzEntity.fromJson(data[i]);
            entities.add(entity);
            print('✅ Parsed ustadz ${i + 1}: ${entity.name}');
          } catch (e) {
            print('❌ Failed to parse ustadz ${i + 1}: $e');
            print('📄 Raw data: ${data[i]}');
            rethrow;
          }
        }

        return entities;
      } else {
        final errorMessage = response.data['message'] ?? 'Failed to fetch ustadz data';
        print('❌ API Error: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('💥 Service Error: $e');
      rethrow;
    }
  }

  Future<UstadzEntity> getDetail(int id) async {
    final response = await _api.dio.get('/ustadz/$id');

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to fetch ustadz detail');
    }
  }

  Future<UstadzEntity> create(Map<String, dynamic> payload) async {
    final response = await _api.dio.post('/ustadz', data: payload);

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to create ustadz');
    }
  }

  Future<UstadzEntity> update(int id, Map<String, dynamic> payload) async {
    final response = await _api.dio.put('/ustadz/$id', data: payload);

    if (response.data['success'] == true) {
      return UstadzEntity.fromJson(response.data['data']);
    } else {
      throw Exception(response.data['message'] ?? 'Failed to update ustadz');
    }
  }

  Future<void> delete(int id) async {
    final response = await _api.dio.delete('/ustadz/$id');

    if (response.data['success'] != true) {
      throw Exception(response.data['message'] ?? 'Failed to delete ustadz');
    }
  }
}
