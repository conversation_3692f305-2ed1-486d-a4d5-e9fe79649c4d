class UserEntity {
  final int id;
  final String name;
  final String email;
  final String role;
  final int? komplekId;

  UserEntity({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.komplekId,
  });

  factory UserEntity.fromJson(Map<String, dynamic> json) {
    return UserEntity(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      role: json['role'] ?? '',
      komplekId: json['komplek_id'],
    );
  }
}
