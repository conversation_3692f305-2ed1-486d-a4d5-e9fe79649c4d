import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';
import 'package:sistem_pondok_apps/domain/entities/pengaturan_tahfidz_entity.dart';
import 'package:sistem_pondok_apps/data/services/pengaturan_tahfidz_service.dart';

class PengaturanTahfidzState {
  final AsyncValue<PengaturanTahfidzEntity?> data;
  final bool isSaving;
  final String? errorMessage;

  PengaturanTahfidzState({
    required this.data,
    this.isSaving = false,
    this.errorMessage,
  });

  PengaturanTahfidzState copyWith({
    AsyncValue<PengaturanTahfidzEntity?>? data,
    bool? isSaving,
    String? errorMessage,
  }) {
    return PengaturanTahfidzState(
      data: data ?? this.data,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage,
    );
  }
}

final pengaturanTahfidzProvider =
    StateNotifierProvider.autoDispose<PengaturanTahfidzNotifier, PengaturanTahfidzState>(
  (ref) => PengaturanTahfidzNotifier(),
);

class PengaturanTahfidzNotifier extends StateNotifier<PengaturanTahfidzState> {
  PengaturanTahfidzNotifier()
      : super(PengaturanTahfidzState(data: const AsyncLoading()));

  final _service = PengaturanTahfidzService();

  Future<void> fetch(int idKomplek) async {
    try {
      state = state.copyWith(data: const AsyncLoading(), errorMessage: null);
      final result = await _service.getPengaturan(idKomplek);
      state = state.copyWith(data: AsyncValue.data(result));
    } catch (e, st) {
      state = state.copyWith(data: AsyncValue.error(e, st));
    }
  }

  Future<void> create(Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.createPengaturan(payload);
      await fetch(payload['id_komplek']);
    } catch (e, st) {
      state = state.copyWith(errorMessage: _parseError(e), data: AsyncValue.error(e, st));
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> update(int id, Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.updatePengaturan(id, payload);
      await fetch(payload['id_komplek']);
    } catch (e, st) {
      state = state.copyWith(errorMessage: _parseError(e), data: AsyncValue.error(e, st));
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> delete(int id, int idKomplek) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.deletePengaturan(id);
      await fetch(idKomplek);
    } catch (e, st) {
      state = state.copyWith(errorMessage: _parseError(e), data: AsyncValue.error(e, st));
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  String? _parseError(Object e) {
    return ErrorHandler.parseError(e);
  }
}
