import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:sistem_pondok_apps/presentation/pages/dashboard_page.dart';
import 'package:sistem_pondok_apps/presentation/pages/login_page.dart';
import 'package:sistem_pondok_apps/presentation/pages/absensi_page.dart';
// import 'package:sistem_pondok_apps/presentation/pages/kelas_page.dart';
// import 'package:sistem_pondok_apps/presentation/pages/ustadz_page.dart';
// import 'package:sistem_pondok_apps/presentation/pages/pengaturan_absensi_page.dart';
// import 'package:sistem_pondok_apps/presentation/pages/profile_page.dart';
import 'package:sistem_pondok_apps/presentation/widgets/app_shell.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> _shellNavigatorKey = GlobalKey<NavigatorState>();

/// Kita buat router pakai Riverpod untuk bisa akses state auth
final routerProvider = Provider<GoRouter>((ref) {
  // Listen to auth state changes untuk trigger router refresh
  ref.listen<AuthState>(authProvider, (previous, next) {
    // Router akan otomatis refresh ketika auth state berubah
  });

  return GoRouter(
    navigatorKey: _rootNavigatorKey,
    initialLocation: '/login',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final auth = ref.read(authProvider);

      // Jika belum selesai initialization, jangan redirect dulu
      if (!auth.isInitialized) {
        return null; // Biarkan tetap di route yang diminta
      }

      final loggingIn = state.fullPath == '/login';
      final isLoggedIn = auth.user != null;

      if (!isLoggedIn && !loggingIn) {
        // Belum login, redirect ke login
        return '/login';
      }
      if (isLoggedIn && loggingIn) {
        // Sudah login, tidak boleh ke login
        return '/dashboard';
      }
      // Tidak redirect
      return null;
    },
    routes: [
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginPage(),
      ),
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return AppShell(child: child);
        },
        routes: [
          GoRoute(
            path: '/dashboard',
            builder: (context, state) => const DashboardPage(),
          ),
          GoRoute(
            path: '/setoran/absensi',
            builder: (context, state) => const AbsensiPage(),
          ),
          // GoRoute(
          //   path: '/setoran/kelas',
          //   builder: (context, state) => const KelasPage(),
          // ),
          // GoRoute(
          //   path: '/setoran/ustadz',
          //   builder: (context, state) => const UstadzPage(),
          // ),
          // GoRoute(
          //   path: '/pengaturan/absensi',
          //   builder: (context, state) => const PengaturanAbsensiPage(),
          // ),
          // GoRoute(
          //   path: '/akun',
          //   builder: (context, state) => const ProfilePage(),
          // ),
        ],
      ),
    ],
  );
});
