import 'package:sistem_pondok_apps/core/network/api_client.dart';
import 'package:sistem_pondok_apps/domain/entities/kelas_entity.dart';

class KelasService {
  final _api = ApiClient();

  Future<List<KelasEntity>> getAll(int komplekId) async {
    final response = await _api.dio.get('/komplek/$komplekId/kelas');
    final data = response.data['data'] as List;
    return data.map((e) => KelasEntity.fromJson(e)).toList();
  }

  Future<void> create(int komplekId, Map<String, dynamic> payload) async {
    await _api.dio.post('/komplek/$komplekId/kelas', data: payload);
  }

  Future<void> update(int komplekId, int id, Map<String, dynamic> payload) async {
    await _api.dio.put('/komplek/$komplekId/kelas/$id', data: payload);
  }

  Future<void> delete(int komplekId, int id) async {
    await _api.dio.delete('/komplek/$komplekId/kelas/$id');
  }
}
