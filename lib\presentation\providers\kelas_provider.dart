import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';
import 'package:sistem_pondok_apps/data/services/kelas_service.dart';
import 'package:sistem_pondok_apps/domain/entities/kelas_entity.dart';

class KelasState {
  final AsyncValue<List<KelasEntity>> data;
  final bool isSaving;
  final String? errorMessage;

  KelasState({
    required this.data,
    this.isSaving = false,
    this.errorMessage,
  });

  KelasState copyWith({
    AsyncValue<List<KelasEntity>>? data,
    bool? isSaving,
    String? errorMessage,
  }) {
    return KelasState(
      data: data ?? this.data,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage,
    );
  }
}

final kelasProvider =
    StateNotifierProvider.autoDispose<KelasNotifier, KelasState>(
  (ref) => KelasNotifier(),
);

class KelasNotifier extends StateNotifier<KelasState> {
  KelasNotifier() : super(KelasState(data: const AsyncLoading()));

  final _service = KelasService();

  Future<void> fetch(int komplekId) async {
    try {
      state = state.copyWith(data: const AsyncLoading(), errorMessage: null);
      final result = await _service.getAll(komplekId);
      state = state.copyWith(data: AsyncValue.data(result));
    } catch (e, st) {
      state = state.copyWith(data: AsyncValue.error(e, st));
    }
  }

  Future<void> create(int komplekId, Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.create(komplekId, payload);
      await fetch(komplekId);
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> update(int komplekId, int id, Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.update(komplekId, id, payload);
      await fetch(komplekId);
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> delete(int komplekId, int id) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.delete(komplekId, id);
      await fetch(komplekId);
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  String? _parseError(Object e) {
    return ErrorHandler.parseError(e);
  }
}
