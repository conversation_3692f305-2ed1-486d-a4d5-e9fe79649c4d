class UstadzEntity {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final bool isConfirmed;
  final String? komplekName;
  final int? komplekId;

  UstadzEntity({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.isConfirmed,
    this.komplekName,
    this.komplekId,
  });

  factory UstadzEntity.fromJson(Map<String, dynamic> json) {
    return UstadzEntity(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      isConfirmed: json['is_confirmed'] ?? false,
      komplekId: json['komplek']?['id'],
      komplekName: json['komplek']?['nama_komplek'],
    );
  }
}
