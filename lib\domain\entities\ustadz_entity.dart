class UstadzEntity {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? emailVerifiedAt;
  final bool isConfirmed;
  final String? komplekName;
  final int? komplekId;
  final String? createdAt;
  final String? updatedAt;

  UstadzEntity({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.emailVerifiedAt,
    required this.isConfirmed,
    this.komplekName,
    this.komplekId,
    this.createdAt,
    this.updatedAt,
  });

  factory UstadzEntity.fromJson(Map<String, dynamic> json) {
    try {
      return UstadzEntity(
        id: json['id'] as int,
        name: json['name'] as String,
        email: json['email'] as String,
        phone: json['phone'] as String?,
        emailVerifiedAt: json['email_verified_at'] as String?,
        // Handle is_confirmed as integer (0/1) or boolean
        isConfirmed: json['is_confirmed'] is int
            ? (json['is_confirmed'] as int) == 1
            : (json['is_confirmed'] as bool? ?? false),
        komplekId: json['komplek']?['id'] as int?,
        komplekName: json['komplek']?['nama_komplek'] as String?,
        createdAt: json['created_at'] as String?,
        updatedAt: json['updated_at'] as String?,
      );
    } catch (e) {
      throw FormatException('Failed to parse UstadzEntity: $e\nJSON: $json');
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'email_verified_at': emailVerifiedAt,
      'is_confirmed': isConfirmed,
      'komplek_id': komplekId,
      'komplek_name': komplekName,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
