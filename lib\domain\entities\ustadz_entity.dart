class UstadzEntity {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? emailVerifiedAt;
  final bool isConfirmed;
  final String? komplekName;
  final int? komplekId;
  final String? createdAt;
  final String? updatedAt;

  UstadzEntity({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.emailVerifiedAt,
    required this.isConfirmed,
    this.komplekName,
    this.komplekId,
    this.createdAt,
    this.updatedAt,
  });

  factory UstadzEntity.fromJson(Map<String, dynamic> json) {
    return UstadzEntity(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      emailVerifiedAt: json['email_verified_at'],
      isConfirmed: json['is_confirmed'] ?? false,
      komplekId: json['komplek']?['id'],
      komplekName: json['komplek']?['nama_komplek'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'email_verified_at': emailVerifiedAt,
      'is_confirmed': isConfirmed,
      'komplek_id': komplekId,
      'komplek_name': komplekName,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }
}
