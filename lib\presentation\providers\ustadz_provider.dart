import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/data/services/ustadz_service.dart';
import 'package:sistem_pondok_apps/domain/entities/ustadz_entity.dart';

class UstadzState {
  final AsyncValue<List<UstadzEntity>> data;
  final bool isSaving;
  final String? errorMessage;

  UstadzState({
    required this.data,
    this.isSaving = false,
    this.errorMessage,
  });

  UstadzState copyWith({
    AsyncValue<List<UstadzEntity>>? data,
    bool? isSaving,
    String? errorMessage,
  }) {
    return UstadzState(
      data: data ?? this.data,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage,
    );
  }
}

final ustadzProvider =
    StateNotifierProvider.autoDispose<UstadzNotifier, UstadzState>(
  (ref) => UstadzNotifier(),
);

class UstadzNotifier extends StateNotifier<UstadzState> {
  UstadzNotifier() : super(UstadzState(data: const AsyncLoading()));

  final _service = UstadzService();

  Future<void> fetch({String? search, int? idKomplek}) async {
    try {
      state = state.copyWith(data: const AsyncLoading(), errorMessage: null);
      final result = await _service.getAll(search: search, idKomplek: idKomplek);
      state = state.copyWith(data: AsyncValue.data(result));
    } catch (e, st) {
      state = state.copyWith(data: AsyncValue.error(e, st));
    }
  }

  Future<void> create(Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.create(payload);
      await fetch();
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> update(int id, Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.update(id, payload);
      await fetch();
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> delete(int id) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.delete(id);
      await fetch();
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  String? _parseError(Object e) {
    if (e is DioException && e.response?.statusCode == 422) {
      final errors = e.response?.data['errors'];
      if (errors is Map) {
        return errors.values.first[0];
      }
    }
    return e.toString();
  }
}
