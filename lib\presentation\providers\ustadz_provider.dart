import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/core/utils/error_handler.dart';
import 'package:sistem_pondok_apps/data/services/ustadz_service.dart';
import 'package:sistem_pondok_apps/domain/entities/ustadz_entity.dart';

class UstadzState {
  final AsyncValue<List<UstadzEntity>> data;
  final bool isSaving;
  final String? errorMessage;

  UstadzState({
    required this.data,
    this.isSaving = false,
    this.errorMessage,
  });

  UstadzState copyWith({
    AsyncValue<List<UstadzEntity>>? data,
    bool? isSaving,
    String? errorMessage,
  }) {
    return UstadzState(
      data: data ?? this.data,
      isSaving: isSaving ?? this.isSaving,
      errorMessage: errorMessage,
    );
  }
}

final ustadzProvider =
    StateNotifierProvider.autoDispose<UstadzNotifier, UstadzState>(
  (ref) => UstadzNotifier(),
);

class UstadzNotifier extends StateNotifier<UstadzState> {
  UstadzNotifier() : super(UstadzState(data: const AsyncLoading()));

  final _service = UstadzService();

  Future<void> fetch({String? search, int? idKomplek}) async {
    try {
      print('🔄 Provider: Starting fetch with idKomplek=$idKomplek, search=$search');
      state = state.copyWith(data: const AsyncLoading(), errorMessage: null);

      final result = await _service.getAll(search: search, idKomplek: idKomplek);
      print('✅ Provider: Successfully fetched ${result.length} ustadz');

      state = state.copyWith(data: AsyncValue.data(result));
    } catch (e, st) {
      print('❌ Provider: Fetch failed with error: $e');
      print('📍 Stack trace: $st');

      final errorMessage = _parseError(e);
      print('🔍 Parsed error message: $errorMessage');

      state = state.copyWith(
        data: AsyncValue.error(e, st),
        errorMessage: errorMessage,
      );
    }
  }

  Future<void> create(Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.create(payload);
      // Refresh data setelah create berhasil
      final currentData = state.data;
      if (currentData is AsyncData<List<UstadzEntity>>) {
        final dataList = currentData.value;
        if (dataList.isNotEmpty) {
          // Jika ada data, fetch ulang dengan parameter yang sama
          final firstItem = dataList.first;
          await fetch(idKomplek: firstItem.komplekId);
        } else {
          await fetch();
        }
      } else {
        await fetch();
      }
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> update(int id, Map<String, dynamic> payload) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.update(id, payload);
      // Refresh data setelah update berhasil
      final currentData = state.data;
      if (currentData is AsyncData<List<UstadzEntity>>) {
        final dataList = currentData.value;
        if (dataList.isNotEmpty) {
          // Jika ada data, fetch ulang dengan parameter yang sama
          final firstItem = dataList.first;
          await fetch(idKomplek: firstItem.komplekId);
        } else {
          await fetch();
        }
      } else {
        await fetch();
      }
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  Future<void> delete(int id) async {
    try {
      state = state.copyWith(isSaving: true, errorMessage: null);
      await _service.delete(id);
      // Refresh data setelah delete berhasil
      final currentData = state.data;
      if (currentData is AsyncData<List<UstadzEntity>>) {
        final dataList = currentData.value;
        if (dataList.isNotEmpty) {
          // Jika ada data, fetch ulang dengan parameter yang sama
          final firstItem = dataList.first;
          await fetch(idKomplek: firstItem.komplekId);
        } else {
          await fetch();
        }
      } else {
        await fetch();
      }
    } catch (e, st) {
      state = state.copyWith(
        errorMessage: _parseError(e),
        data: AsyncValue.error(e, st),
      );
    } finally {
      state = state.copyWith(isSaving: false);
    }
  }

  String? _parseError(Object e) {
    return ErrorHandler.parseError(e);
  }
}
