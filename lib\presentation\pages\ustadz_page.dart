import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:sistem_pondok_apps/presentation/providers/ustadz_provider.dart';
import 'package:sistem_pondok_apps/presentation/providers/auth_provider.dart';
import 'package:sistem_pondok_apps/presentation/widgets/error_state_widget.dart';

class UstadzPage extends ConsumerStatefulWidget {
  const UstadzPage({super.key});

  @override
  ConsumerState<UstadzPage> createState() => _UstadzPageState();
}

class _UstadzPageState extends ConsumerState<UstadzPage> with ErrorHandlerMixin {
  @override
  void initState() {
    super.initState();
    // Fetch data saat halaman pertama kali muncul
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = ref.read(authProvider).user;
      if (user != null && user.komplekId != null) {
        ref.read(ustadzProvider.notifier).fetch(idKomplek: user.komplekId!);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final ustadzState = ref.watch(ustadzProvider);
    final user = ref.watch(authProvider).user;
    final theme = Theme.of(context);
    final isDesktop = MediaQuery.of(context).size.width > 768;

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [
          // Header Section
          _buildHeader(theme, ustadzState, user),

          // Content
          Expanded(
            child: ustadzState.data.when(
              loading: () => _buildLoadingState(theme),
              error: (e, _) => _buildErrorState(e, user),
              data: (data) {
                if (data.isEmpty) {
                  return _buildEmptyState(theme, ustadzState);
                }
                return _buildUstadzList(theme, data, isDesktop);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(theme, ustadzState),
    );
  }

  /// Build header section dengan search dan refresh
  Widget _buildHeader(ThemeData theme, UstadzState ustadzState, dynamic user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.people,
                  color: theme.colorScheme.onPrimaryContainer,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Kelola Ustadz',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Kelola data ustadz di komplek Anda',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton.filledTonal(
                onPressed: () {
                  if (user != null && user.komplekId != null) {
                    ref.read(ustadzProvider.notifier).fetch(idKomplek: user.komplekId!);
                  }
                },
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh Data',
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Memuat data ustadz...',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error state
  Widget _buildErrorState(dynamic error, dynamic user) {
    return ErrorStateWidget(
      error: error,
      onRetry: () {
        if (user != null && user.komplekId != null) {
          ref.read(ustadzProvider.notifier).fetch(idKomplek: user.komplekId!);
        }
      },
    );
  }

  /// Build empty state
  Widget _buildEmptyState(ThemeData theme, UstadzState ustadzState) {
    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon dengan gradient background
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary.withValues(alpha: 0.1),
                      theme.colorScheme.secondary.withValues(alpha: 0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(60),
                ),
                child: Icon(
                  Icons.people_outline,
                  size: 60,
                  color: theme.colorScheme.primary,
                ),
              ),

              const SizedBox(height: 32),

              // Title
              Text(
                'Belum Ada Ustadz',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 12),

              // Subtitle
              Text(
                'Belum ada ustadz yang terdaftar di komplek ini.\nTambahkan ustadz baru untuk memulai.',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // Error message jika ada
              if (ustadzState.errorMessage != null) ...[
                CompactErrorWidget(
                  error: ustadzState.errorMessage!,
                ),
                const SizedBox(height: 24),
              ],

              // Add button
              FilledButton.icon(
                onPressed: ustadzState.isSaving ? null : () => _showCreateOrEditDialog(),
                icon: ustadzState.isSaving
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: theme.colorScheme.onPrimary,
                        ),
                      )
                    : const Icon(Icons.add),
                label: Text(ustadzState.isSaving ? 'Menambahkan...' : 'Tambah Ustadz'),
                style: FilledButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  textStyle: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build ustadz list dengan design yang modern
  Widget _buildUstadzList(ThemeData theme, List<dynamic> data, bool isDesktop) {
    return Padding(
      padding: EdgeInsets.all(isDesktop ? 24 : 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Stats header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.people,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${data.length} Ustadz Terdaftar',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // List
          Expanded(
            child: isDesktop ? _buildDesktopGrid(theme, data) : _buildMobileList(theme, data),
          ),
        ],
      ),
    );
  }

  /// Build desktop grid layout
  Widget _buildDesktopGrid(ThemeData theme, List<dynamic> data) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5,
      ),
      itemCount: data.length,
      itemBuilder: (context, index) {
        final ustadz = data[index];
        return _buildUstadzCard(theme, ustadz);
      },
    );
  }

  /// Build mobile list layout
  Widget _buildMobileList(ThemeData theme, List<dynamic> data) {
    return ListView.separated(
      itemCount: data.length,
      separatorBuilder: (_, __) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final ustadz = data[index];
        return _buildUstadzCard(theme, ustadz);
      },
    );
  }

  /// Build ustadz card
  Widget _buildUstadzCard(ThemeData theme, dynamic ustadz) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Avatar
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.person,
                color: theme.colorScheme.onPrimaryContainer,
                size: 24,
              ),
            ),

            const SizedBox(width: 16),

            // Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ustadz.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    ustadz.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  if (ustadz.phone != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      ustadz.phone!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // Status badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: ustadz.isConfirmed
                    ? Colors.green.withValues(alpha: 0.1)
                    : Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: ustadz.isConfirmed
                      ? Colors.green.withValues(alpha: 0.3)
                      : Colors.orange.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                ustadz.isConfirmed ? 'Aktif' : 'Pending',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: ustadz.isConfirmed ? Colors.green[700] : Colors.orange[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            const SizedBox(width: 8),

            // Actions
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'edit') {
                  _showCreateOrEditDialog(ustadz: ustadz);
                } else if (value == 'delete') {
                  _confirmDelete(ustadz.id);
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16, color: theme.colorScheme.primary),
                      const SizedBox(width: 8),
                      const Text('Edit'),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: theme.colorScheme.error),
                      const SizedBox(width: 8),
                      const Text('Hapus'),
                    ],
                  ),
                ),
              ],
              child: Icon(
                Icons.more_vert,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton(ThemeData theme, UstadzState ustadzState) {
    return FloatingActionButton.extended(
      onPressed: ustadzState.isSaving ? null : () => _showCreateOrEditDialog(),
      icon: ustadzState.isSaving
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: theme.colorScheme.onPrimary,
              ),
            )
          : const Icon(Icons.add),
      label: Text(ustadzState.isSaving ? 'Menambahkan...' : 'Tambah Ustadz'),
    );
  }

  void _showCreateOrEditDialog({ustadz}) {
    final theme = Theme.of(context);
    final user = ref.read(authProvider).user;

    if (user?.komplekId == null) {
      showErrorSnackBar('ID Komplek tidak ditemukan. Silakan login ulang.');
      return;
    }

    showDialog(
      context: context,
      builder: (context) {
        final formKey = GlobalKey<FormState>();
        String name = ustadz?.name ?? '';
        String email = ustadz?.email ?? '';
        String? phone = ustadz?.phone ?? '';
        String password = '';
        bool showPassword = false;

        return StatefulBuilder(
          builder: (context, setState) {
            final saving = ref.watch(ustadzProvider).isSaving;

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500),
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            ustadz == null ? Icons.person_add : Icons.edit,
                            color: theme.colorScheme.onPrimaryContainer,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                ustadz == null ? 'Tambah Ustadz' : 'Edit Ustadz',
                                style: theme.textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                ustadz == null
                                    ? 'Tambahkan ustadz baru ke komplek'
                                    : 'Ubah informasi ustadz',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Error message
                    if (ref.watch(ustadzProvider).errorMessage != null) ...[
                      CompactErrorWidget(
                        error: ref.watch(ustadzProvider).errorMessage!,
                      ),
                      const SizedBox(height: 16),
                    ],

                    // Form
                    Form(
                      key: formKey,
                      child: Column(
                        children: [
                          // Nama Field
                          TextFormField(
                            initialValue: name,
                            decoration: InputDecoration(
                              labelText: 'Nama Lengkap',
                              hintText: 'Masukkan nama lengkap ustadz',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.person_outline),
                            ),
                            onChanged: (v) => name = v,
                            validator: (v) => v == null || v.isEmpty ? 'Nama wajib diisi' : null,
                          ),

                          const SizedBox(height: 16),

                          // Email Field
                          TextFormField(
                            initialValue: email,
                            decoration: InputDecoration(
                              labelText: 'Email',
                              hintText: 'Masukkan alamat email',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.email_outlined),
                            ),
                            keyboardType: TextInputType.emailAddress,
                            onChanged: (v) => email = v,
                            validator: (v) {
                              if (v == null || v.isEmpty) return 'Email wajib diisi';
                              if (!v.contains('@')) return 'Format email tidak valid';
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),

                          // Phone Field
                          TextFormField(
                            initialValue: phone,
                            decoration: InputDecoration(
                              labelText: 'No. HP (Opsional)',
                              hintText: 'Masukkan nomor HP',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.phone_outlined),
                            ),
                            keyboardType: TextInputType.phone,
                            onChanged: (v) => phone = v.isEmpty ? null : v,
                          ),

                          const SizedBox(height: 16),

                          // Password Field
                          TextFormField(
                            obscureText: !showPassword,
                            decoration: InputDecoration(
                              labelText: ustadz == null ? 'Password' : 'Password Baru (Opsional)',
                              hintText: ustadz == null
                                  ? 'Masukkan password'
                                  : 'Kosongkan jika tidak ingin mengubah',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              prefixIcon: const Icon(Icons.lock_outline),
                              suffixIcon: IconButton(
                                icon: Icon(
                                  showPassword ? Icons.visibility_off : Icons.visibility,
                                ),
                                onPressed: () {
                                  setState(() {
                                    showPassword = !showPassword;
                                  });
                                },
                              ),
                            ),
                            onChanged: (v) => password = v,
                            validator: (v) {
                              if (ustadz == null && (v == null || v.isEmpty)) {
                                return 'Password wajib diisi';
                              }
                              if (v != null && v.isNotEmpty && v.length < 6) {
                                return 'Password minimal 6 karakter';
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Actions
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: saving ? null : () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: const Text('Batal'),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          flex: 2,
                          child: FilledButton.icon(
                            onPressed: saving
                                ? null
                                : () async {
                                    if (!formKey.currentState!.validate()) return;

                                    final payload = {
                                      'name': name,
                                      'email': email,
                                      if (phone != null && phone!.isNotEmpty) 'phone': phone,
                                      if (password.isNotEmpty) 'password': password,
                                      'id_komplek': user!.komplekId,
                                    };

                                    final navigator = Navigator.of(context);
                                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                                    try {
                                      if (ustadz == null) {
                                        await ref.read(ustadzProvider.notifier).create(payload);
                                      } else {
                                        await ref.read(ustadzProvider.notifier).update(
                                              ustadz.id,
                                              payload,
                                            );
                                      }

                                      if (mounted) {
                                        navigator.pop();
                                        scaffoldMessenger.showSnackBar(
                                          SnackBar(
                                            content: Row(
                                              children: [
                                                const Icon(Icons.check_circle, color: Colors.white),
                                                const SizedBox(width: 12),
                                                Text(ustadz == null
                                                    ? 'Ustadz berhasil ditambahkan'
                                                    : 'Ustadz berhasil diperbarui'),
                                              ],
                                            ),
                                            backgroundColor: Colors.green,
                                            behavior: SnackBarBehavior.floating,
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (mounted) {
                                        showErrorSnackBar(e);
                                      }
                                    }
                                  },
                            icon: saving
                                ? SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      color: theme.colorScheme.onPrimary,
                                    ),
                                  )
                                : const Icon(Icons.save),
                            label: Text(saving ? 'Menyimpan...' : 'Simpan'),
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _confirmDelete(int id) async {
    final theme = Theme.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: theme.colorScheme.error,
            ),
            const SizedBox(width: 12),
            const Text('Hapus Ustadz'),
          ],
        ),
        content: const Text(
          'Yakin ingin menghapus ustadz ini? Tindakan ini tidak dapat dibatalkan.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Batal'),
          ),
          FilledButton(
            onPressed: () => Navigator.pop(context, true),
            style: FilledButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
            ),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      try {
        await ref.read(ustadzProvider.notifier).delete(id);
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Row(
                children: [
                  Icon(Icons.delete, color: Colors.white),
                  SizedBox(width: 12),
                  Text('Ustadz berhasil dihapus'),
                ],
              ),
              backgroundColor: theme.colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar(e);
        }
      }
    }
  }
}
